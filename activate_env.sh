#!/bin/bash

# EvoAgentX 虚拟环境激活脚本
# 使用方法: source activate_env.sh

echo "🚀 激活 EvoAgentX 虚拟环境..."

# 检查虚拟环境是否存在
if [ ! -d "evoagentx" ]; then
    echo "❌ 虚拟环境 'evoagentx' 不存在！"
    echo "请先运行以下命令创建虚拟环境："
    echo "python3.13 -m venv evoagentx"
    echo "source evoagentx/bin/activate"
    echo "pip install -r requirements.txt"
    return 1
fi

# 激活虚拟环境
source evoagentx/bin/activate

# 检查激活是否成功
if [ "$VIRTUAL_ENV" != "" ]; then
    echo "✅ 虚拟环境已激活: $VIRTUAL_ENV"
    echo "🐍 Python版本: $(python --version)"
    echo ""
    echo "📝 常用命令:"
    echo "  测试环境:     python test_environment.py"
    echo "  运行示例:     python examples/workflow_demo.py"
    echo "  查看文档:     cat docs/quickstart.md"
    echo "  退出环境:     deactivate"
    echo ""
    echo "💡 提示: 记得在 .env 文件中配置你的API密钥"
else
    echo "❌ 虚拟环境激活失败！"
    return 1
fi
