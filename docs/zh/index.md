# **EvoAgentX**

<p align="center" style="font-size: 1.0rem;">
  <em>一个用于评估和演进代理工作流的自动化框架。</em>
</p>

<p align="center">
  <img src="./assets/framework_zh.jpg">
</p>


## 🚀 简介

EvoAgentX 是一个开源框架，旨在自动化代理工作流的生成、执行、评估和优化。通过利用大语言模型（LLMs），EvoAgentX 使开发者和研究人员能够快速构建、测试和部署多代理系统，这些系统可以随着时间推移在复杂性和能力上不断增长。

## ✨ 主要特性

- **简单的代理和工作流定制**：使用自然语言提示轻松创建自定义代理和工作流。EvoAgentX 让你能够轻松地将高层次想法转化为可工作的系统。
- **自动工作流生成与执行**：从简单的目标描述自动生成和执行代理工作流，减少多代理系统设计中的手动工作量。
- **工作流优化**：集成现有工作流优化技术，通过迭代优化提升工作流性能。
- **基准测试与评估**：包含内置基准测试和标准化评估指标，用于衡量不同任务和代理配置下的工作流效果。
- **工作流执行工具包**：提供执行复杂工作流所需的一系列工具，如搜索组件和模型上下文协议（MCP）。

## 🔍 工作原理

EvoAgentX 使用模块化架构，包含以下核心组件：

1. **工作流生成器**：根据你的目标创建代理工作流
2. **代理管理器**：处理代理的创建、配置和部署
3. **工作流执行器**：高效运行工作流，确保代理间正确通信
4. **评估器**：提供性能指标和改进建议
5. **优化器**：通过不断演进提升工作流性能

## 👥 社区

- **Discord**：加入我们的 [Discord 频道](https://discord.gg/w3x2YrCa) 进行讨论和获取支持
- **GitHub**：在 [GitHub](https://github.com/EvoAgentX/EvoAgentX/) 上为项目做出贡献
- **Email**：通过 [<EMAIL>](mailto:<EMAIL>) 联系我们
- **微信**：通过 [微信](https://github.com/EvoAgentX/EvoAgentX/blob/main/assets/wechat_info.md) 获取更新和支持。

## 🤝 贡献

我们欢迎社区贡献！请参阅我们的[贡献指南](https://github.com/EvoAgentX/EvoAgentX/blob/main/CONTRIBUTING.md)了解更多详情。
