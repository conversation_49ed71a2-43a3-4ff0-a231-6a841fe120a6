name: Deploy MkDocs Site
on:
  push:
    branches:
      - master 
      - main
permissions:
  contents: write
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"

      - run: echo "cache_id=$(date --utc '+%V')" >> $GITHUB_ENV 
      - uses: actions/cache@v4
        with:
          key: mkdocs-material-${{ env.cache_id }}
          path: .cache 
          restore-keys: |
            mkdocs-material-

      - name: Install MkDocs and plugins
        run: |
          pip install --upgrade pip
          pip install \
            mkdocs==1.6.1 \
            mkdocs-autorefs==1.4.1 \
            mkdocs-get-deps==0.2.0 \
            mkdocs-git-committers-plugin-2==2.5.0 \
            mkdocs-material==9.6.13 \
            mkdocs-material-extensions==1.3.1 \
            mkdocs-static-i18n==1.3.0 \
            mkdocstrings==0.29.1 \
            mkdocstrings-python==1.16.10
      - name: Deploy to GitHub Pages
        run: mkdocs gh-deploy --force