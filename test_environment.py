#!/usr/bin/env python3
"""
测试EvoAgentX环境是否正确配置
"""

import sys
import os

def test_imports():
    """测试主要依赖包的导入"""
    print("🔍 测试依赖包导入...")
    
    try:
        import evoagentx
        print("✅ evoagentx 导入成功")
    except ImportError as e:
        print(f"❌ evoagentx 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ numpy {np.__version__} 导入成功")
    except ImportError as e:
        print(f"❌ numpy 导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        print(f"✅ pandas {pd.__version__} 导入成功")
    except ImportError as e:
        print(f"❌ pandas 导入失败: {e}")
        return False
    
    try:
        import torch
        print(f"✅ torch {torch.__version__} 导入成功")
    except ImportError as e:
        print(f"❌ torch 导入失败: {e}")
        return False
    
    try:
        import transformers
        print(f"✅ transformers {transformers.__version__} 导入成功")
    except ImportError as e:
        print(f"❌ transformers 导入失败: {e}")
        return False
    
    try:
        import faiss
        print(f"✅ faiss {faiss.__version__} 导入成功")
    except ImportError as e:
        print(f"❌ faiss 导入失败: {e}")
        return False
    
    try:
        import openai
        print(f"✅ openai {openai.__version__} 导入成功")
    except ImportError as e:
        print(f"❌ openai 导入失败: {e}")
        return False
    
    try:
        import litellm
        # litellm 没有 __version__ 属性，所以只显示导入成功
        print("✅ litellm 导入成功")
    except ImportError as e:
        print(f"❌ litellm 导入失败: {e}")
        return False
    
    return True

def test_evoagentx_modules():
    """测试EvoAgentX核心模块"""
    print("\n🔍 测试EvoAgentX核心模块...")
    
    try:
        from evoagentx.models import OpenAILLMConfig, OpenAILLM
        print("✅ evoagentx.models 导入成功")
    except ImportError as e:
        print(f"❌ evoagentx.models 导入失败: {e}")
        return False
    
    try:
        from evoagentx.workflow import WorkFlowGenerator, WorkFlowGraph, WorkFlow
        print("✅ evoagentx.workflow 导入成功")
    except ImportError as e:
        print(f"❌ evoagentx.workflow 导入失败: {e}")
        return False
    
    try:
        from evoagentx.agents import AgentManager
        print("✅ evoagentx.agents 导入成功")
    except ImportError as e:
        print(f"❌ evoagentx.agents 导入失败: {e}")
        return False
    
    try:
        from evoagentx.actions.code_extraction import CodeExtraction
        from evoagentx.actions.code_verification import CodeVerification
        print("✅ evoagentx.actions 导入成功")
    except ImportError as e:
        print(f"❌ evoagentx.actions 导入失败: {e}")
        return False
    
    return True

def test_environment_info():
    """显示环境信息"""
    print("\n📋 环境信息:")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查CUDA支持
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
            print(f"CUDA版本: {torch.version.cuda}")
        else:
            print("⚠️  CUDA不可用，将使用CPU")
    except:
        print("⚠️  无法检查CUDA状态")

def main():
    """主测试函数"""
    print("🚀 EvoAgentX环境测试开始\n")
    
    # 测试基本导入
    if not test_imports():
        print("\n❌ 基本依赖测试失败")
        return False
    
    # 测试EvoAgentX模块
    if not test_evoagentx_modules():
        print("\n❌ EvoAgentX模块测试失败")
        return False
    
    # 显示环境信息
    test_environment_info()
    
    print("\n🎉 所有测试通过！EvoAgentX环境配置成功！")
    print("\n📝 下一步:")
    print("1. 创建 .env 文件并配置API密钥")
    print("2. 运行示例: python examples/workflow_demo.py")
    print("3. 查看文档: docs/quickstart.md")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
